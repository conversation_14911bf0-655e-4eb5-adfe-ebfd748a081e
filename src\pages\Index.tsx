import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Sprout, Users, Smartphone, Truck, ArrowRight, Leaf, ShoppingCart, MapPin } from 'lucide-react';
import { Link } from 'react-router-dom';
import FloatingNavbar from '@/components/FloatingNavbar';
import BottomBlurOverlay from '@/components/BottomBlurOverlay';
import SupplyChainSection from '@/components/SupplyChainSection';

const Index = () => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);

  const businessPillars = [
    {
      icon: <Sprout className="w-8 h-8" />,
      title: "Direct from Farmers",
      subtitle: "Ethical Procurement",
      description: "We work directly with local farmers to source natural, chemical-free vegetables, ensuring fair compensation and eliminating middlemen.",
      stats: "Higher-than-market prices for farmers",
      color: "from-primary-400 to-primary-600"
    },
    {
      icon: <ShoppingCart className="w-8 h-8" />,
      title: "B2B Partner Network",
      subtitle: "Empowering Local Stores",
      description: "Medium-sized grocery stores get access to premium vegetables and digital listing on our platform to compete with major delivery apps.",
      stats: "Wholesale rates + Digital expansion",
      color: "from-secondary-400 to-secondary-600"
    },
    {
      icon: <Smartphone className="w-8 h-8" />,
      title: "Tech Platform",
      subtitle: "3-Way Integration",
      description: "Customer app, business partner web app, and delivery partner app create a seamless ecosystem for all stakeholders.",
      stats: "Real-time management & tracking",
      color: "from-accent-400 to-accent-600"
    },
    {
      icon: <Truck className="w-8 h-8" />,
      title: "Delivery Network",
      subtitle: "Local Youth Powered",
      description: "We train and onboard local youngsters as delivery partners, providing consistent earning opportunities and career growth.",
      stats: "Hyperlocal & fast delivery",
      color: "from-primary-500 to-secondary-500"
    }
  ];

  const impacts = [
    { label: "Farmer Income", value: "+40%", description: "Above market rates" },
    { label: "Delivery Time", value: "30min", description: "Average delivery" },
    { label: "Youth Jobs", value: "500+", description: "Employment created" },
    { label: "Partner Stores", value: "50+", description: "In Panipat" }
  ];

  return (
    <div className="min-h-screen">
      <FloatingNavbar />
      {/* Hero Section with Modern UI */}
      <section className="relative min-h-screen overflow-hidden">
        {/* Background Image */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('/leafy-greens-hero1.jpg')`
          }}
        >
          {/* Overlay for better contrast */}
          <div className="absolute inset-0 bg-black/20"></div>
        </div>

        {/* Main Content Container */}
        <div className="relative z-10 min-h-screen flex items-center justify-center p-4 md:p-8">
          <div className="w-full max-w-7xl mx-auto">
            
            {/* White Rounded Container */}
            <div className="bg-white rounded-3xl md:rounded-[3rem] shadow-2xl overflow-hidden">
              
              {/* Header Navigation */}
              <div className="px-6 md:px-12 pt-6 md:pt-8 pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 mx-auto md:mx-0">
                    <div className="w-12 h-12 md:w-8 md:h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                      <Leaf className="w-7 h-7 md:w-5 md:h-5 text-white" />
                    </div>
                    <span className="text-2xl md:text-xl lg:text-2xl font-bold text-gray-800">Foodhub Trading</span>
                  </div>
                  
                  {/* Navigation Pills */}
                  <div className="hidden md:flex items-center space-x-2">
                    <Link to="/career">
                      <Button variant="ghost" className="rounded-full px-6 py-2 text-gray-600 hover:bg-gray-100">
                        Career
                      </Button>
                    </Link>
                    <Link to="/partner">
                      <Button variant="ghost" className="rounded-full px-6 py-2 text-gray-600 hover:bg-gray-100">
                        Partner
                      </Button>
                    </Link>
                    <Link to="/about">
                      <Button variant="ghost" className="rounded-full px-6 py-2 text-gray-600 hover:bg-gray-100">
                        About
                      </Button>
                    </Link>
                    <Link to="/contact">
                      <Button variant="ghost" className="rounded-full px-6 py-2 text-gray-600 hover:bg-gray-100">
                        Contact
                      </Button>
                    </Link>
                    <Button className="rounded-full bg-gray-800 hover:bg-gray-900 text-white px-6 py-2 ml-4" onClick={() => {
                        window.open('https://wa.me/+919720680037', '_blank');
                      }}>
                      • LET'S TALK!
                    </Button>
                  </div>
                </div>
              </div>

              {/* Main Content Grid */}
              <div className="grid lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 px-4 sm:px-6 md:px-12 pb-6 sm:pb-8 md:pb-12">
                
                {/* Left Content */}
                <div className="space-y-8 text-center md:text-left">
                  <div>
                    <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-7xl font-black text-gray-900 leading-none mb-4 sm:mb-6">
                      From{' '}
                      <span className="bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">
                        Farm fields
                      </span> to
                      <br />
                      your family’s table
                    </h1>
                    
                    <p className="text-base sm:text-lg md:text-xl text-gray-600 leading-relaxed max-w-lg mx-auto md:mx-0">
                      Revolutionizing how fresh vegetables move from farmers to retailers and consumers through ethical procurement, 
                      technology, and hyperlocal delivery.
                    </p>
                  </div>

                  <div className="flex justify-center md:justify-start">
                    <Button className="bg-primary-500 hover:bg-primary-600 text-white rounded-full px-8 py-4 text-lg font-semibold">
                      GET STARTED
                      <ArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </div>
                </div>

                {/* Right Side - Interactive Elements */}
                <div className="relative">
                  {/* Main Visual Element */}
                  <div className="relative">
                    {/* Background Image */}
                    <div
                      className="w-full h-96 sm:h-[28rem] md:h-[32rem] lg:h-[36rem] xl:h-[30rem] bg-cover bg-center rounded-3xl relative overflow-hidden"
                      style={{
                        backgroundImage: `url('/hero-image.png')`
                      }}
                    >
                      {/* Overlay */}
                      <div className="absolute inset-0 bg-primary-500/20"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Supply Chain Visual Section */}
      <SupplyChainSection />

      {/* Stats Section */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {impacts.map((impact, index) => (
              <div key={index} className="text-center group">
                <div className="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-2xl p-6 mb-4 group-hover:shadow-lg transition-all duration-300">
                  <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">{impact.value}</div>
                  <div className="text-gray-800 font-semibold mb-1">{impact.label}</div>
                  <div className="text-sm text-gray-500">{impact.description}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-white to-primary-50/30">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">

            {/* Left Content */}
            <div className="space-y-8">
              <div>
                <h2 className="text-6xl md:text-7xl lg:text-8xl font-black text-gray-900 leading-none mb-4">
                  Sustainable
                  <br />
                  <span className="bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">
                    Agriculture
                  </span>
                </h2>

                <div className="relative">
                  <div className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-primary-400/20 to-secondary-400/20 rounded-full blur-xl"></div>
                  <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-lg relative z-10">
                    Welcome to a revolutionary journey that transcends traditional farming.
                    Discover the artistry of sustainable agriculture captured through innovation and technology.
                  </p>
                </div>
              </div>

              {/* Social Links */}
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-white rounded-full border-2 border-gray-200 flex items-center justify-center hover:border-primary-400 transition-colors cursor-pointer group">
                  <span className="text-sm font-bold text-gray-600 group-hover:text-primary-600">yt</span>
                </div>
                <div className="w-12 h-12 bg-white rounded-full border-2 border-gray-200 flex items-center justify-center hover:border-primary-400 transition-colors cursor-pointer group">
                  <span className="text-sm font-bold text-gray-600 group-hover:text-primary-600">ig</span>
                </div>
                <div className="w-12 h-12 bg-white rounded-full border-2 border-gray-200 flex items-center justify-center hover:border-primary-400 transition-colors cursor-pointer group">
                  <span className="text-sm font-bold text-gray-600 group-hover:text-primary-600">fb</span>
                </div>
                <div className="w-12 h-12 bg-white rounded-full border-2 border-gray-200 flex items-center justify-center hover:border-primary-400 transition-colors cursor-pointer group">
                  <span className="text-sm font-bold text-gray-600 group-hover:text-primary-600">x</span>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-8">
                <div className="text-center">
                  <div className="text-5xl md:text-6xl font-black text-gray-900 mb-2">+250k</div>
                  <div className="text-sm text-gray-600 font-medium">Farmers reaching wide</div>
                  <div className="text-sm text-gray-600">audience and creating lasting impression</div>
                </div>
                <div className="text-center">
                  <div className="text-5xl md:text-6xl font-black text-gray-900 mb-2">+800k</div>
                  <div className="text-sm text-gray-600 font-medium">Hours invested, engaging</div>
                  <div className="text-sm text-gray-600">storytelling that captivates viewers</div>
                </div>
              </div>
            </div>

            {/* Right Content - Image with Overlays */}
            <div className="relative">
              <div className="relative bg-gradient-to-br from-primary-400 to-secondary-500 rounded-[3rem] p-8 overflow-hidden">
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-10">
                  <div className="absolute top-10 left-10 w-20 h-20 border-2 border-white rounded-full"></div>
                  <div className="absolute bottom-20 right-10 w-16 h-16 border-2 border-white rounded-full"></div>
                  <div className="absolute top-1/2 right-20 w-12 h-12 border-2 border-white rounded-full"></div>
                </div>

                {/* Main Image Placeholder */}
                <div className="relative bg-white/10 backdrop-blur-sm rounded-3xl p-8 min-h-[400px] flex items-center justify-center">
                  <div className="text-center text-white">
                    <Leaf className="w-24 h-24 mx-auto mb-4 opacity-80" />
                    <p className="text-lg font-semibold opacity-90">Sustainable Farming</p>
                    <p className="text-sm opacity-70">Innovation in Action</p>
                  </div>
                </div>

                {/* Floating Elements */}
                <div className="absolute top-8 right-8 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-lg animate-float">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center mb-2">
                    <Sprout className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-xs text-gray-600 font-medium">Fresh Produce</p>
                </div>

                <div className="absolute bottom-8 left-8 bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-lg animate-float" style={{ animationDelay: '2s' }}>
                  <div className="w-12 h-12 bg-gradient-to-br from-secondary-500 to-accent-500 rounded-xl flex items-center justify-center mb-2">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <p className="text-xs text-gray-600 font-medium">Community Impact</p>
                </div>

                <div className="absolute top-1/2 left-4 bg-white/90 backdrop-blur-sm rounded-2xl p-3 shadow-lg animate-float" style={{ animationDelay: '4s' }}>
                  <div className="w-10 h-10 bg-gradient-to-br from-accent-500 to-primary-500 rounded-xl flex items-center justify-center mb-1">
                    <Truck className="w-5 h-5 text-white" />
                  </div>
                  <p className="text-xs text-gray-600 font-medium">Fast Delivery</p>
                </div>

                {/* Arrow Navigation */}
                <div className="absolute bottom-8 right-8 w-12 h-12 bg-gray-900 rounded-full flex items-center justify-center cursor-pointer hover:bg-gray-800 transition-colors">
                  <ArrowRight className="w-5 h-5 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Business Model Section */}
      <section id="model" className="py-20 px-4 bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              Our <span className="bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">4-Pillar</span> Business Model
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              A comprehensive ecosystem that connects farmers, retailers, technology, and delivery partners for sustainable growth.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {businessPillars.map((pillar, index) => (
              <Card 
                key={index}
                className={`group cursor-pointer transition-all duration-500 hover:shadow-2xl ${
                  hoveredCard === index ? 'scale-105' : ''
                }`}
                onMouseEnter={() => setHoveredCard(index)}
                onMouseLeave={() => setHoveredCard(null)}
              >
                <CardContent className="p-8">
                  <div className={`w-16 h-16 bg-gradient-to-br ${pillar.color} rounded-2xl flex items-center justify-center text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    {pillar.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">{pillar.title}</h3>
                  <p className="text-primary-600 font-semibold mb-4">{pillar.subtitle}</p>
                  <p className="text-gray-600 mb-4 leading-relaxed">{pillar.description}</p>
                  <Badge variant="secondary" className="bg-primary-100 text-primary-700">
                    {pillar.stats}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* What Sets Us Apart */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">What Sets Us Apart</h2>
            <p className="text-xl text-gray-600">Creating value for every stakeholder in the supply chain</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { title: "Farmer Impact", value: "Higher income, consistent demand, no middlemen", icon: <Sprout className="w-6 h-6" /> },
              { title: "Retailer Benefit", value: "Hassle-free sourcing, digital listing, better margins", icon: <ShoppingCart className="w-6 h-6" /> },
              { title: "Customer Value", value: "Fresh, quality produce at competitive prices", icon: <Users className="w-6 h-6" /> },
              { title: "Youth Employment", value: "Flexible income through delivery roles", icon: <Truck className="w-6 h-6" /> },
              { title: "Technology", value: "All-in-one platform for procurement, sales, fulfillment", icon: <Smartphone className="w-6 h-6" /> },
              { title: "Social Mission", value: "Solving real problems across the value chain", icon: <Leaf className="w-6 h-6" /> }
            ].map((item, index) => (
              <div key={index} className="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 group">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                  {item.icon}
                </div>
                <h3 className="text-lg font-bold text-gray-800 mb-2">{item.title}</h3>
                <p className="text-gray-600">{item.value}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Launch Location */}
      <section className="py-20 px-4 bg-gradient-to-br from-secondary-50 to-accent-50">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <Badge className="mb-6 bg-secondary-100 text-secondary-700 hover:bg-secondary-200 px-4 py-2">
              <MapPin className="w-4 h-4 mr-2" />
              Pilot City
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">Starting in Panipat</h2>
            <p className="text-xl text-gray-600 mb-8">
              We're launching in Panipat, a growing urban hub, to pilot and perfect our model. 
              Once optimized, the same structure is scalable to other Tier 2 & Tier 3 cities across India.
            </p>
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-800 mb-4">Our Vision</h3>
              <blockquote className="text-xl text-gray-600 italic">
                "To create a tech-enabled, farmer-first food supply chain that empowers every link — from soil to doorstep."
              </blockquote>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-primary-500 to-secondary-500">
        <div className="container mx-auto text-center">
          <div className="max-w-3xl mx-auto text-white">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Transform the Food Supply Chain?</h2>
            <p className="text-xl mb-8 opacity-90">
              Join us in creating a sustainable, technology-driven food supply chain that benefits everyone.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button size="lg" variant="secondary" className="bg-white text-primary-600 hover:bg-gray-100 px-8 py-4 text-lg">
                Partner with Us
              </Button>
              <Button size="lg" variant="outline" className="border-white text-primary-600 hover:bg-gray/100 px-8 py-4 text-lg">
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 bg-gray-900 text-white">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                <Leaf className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold">Foodhub Trading</span>
            </div>
            <div className="text-gray-400 text-center md:text-right">
              <p>&copy; 2024 Foodhub Trading. Revolutionizing agriculture, one delivery at a time.</p>
              <p className="mt-2">📍 Panipat, Haryana | 🌱 Farm to Table Technology</p>
            </div>
          </div>
        </div>
      </footer>

      <BottomBlurOverlay />
    </div>
  );
};

export default Index;
