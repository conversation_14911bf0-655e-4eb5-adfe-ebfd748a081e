import React, { useState, useEffect } from 'react';
import SplashScreen from './SplashScreen';

interface SplashWrapperProps {
  children: React.ReactNode;
}

const SplashWrapper: React.FC<SplashWrapperProps> = ({ children }) => {
  const [showSplash, setShowSplash] = useState(true);
  const [hasShownSplash, setHasShownSplash] = useState(false);

  useEffect(() => {
    // Only show splash on the very first load of the website
    if (!hasShownSplash) {
      setShowSplash(true);
      setHasShownSplash(true);
    }
  }, [hasShownSplash]);

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  return (
    <>
      {showSplash && <SplashScreen onComplete={handleSplashComplete} />}
      <div className={showSplash ? 'opacity-0 pointer-events-none' : 'opacity-100 transition-opacity duration-300'}>
        {children}
      </div>
    </>
  );
};

export default SplashWrapper;
