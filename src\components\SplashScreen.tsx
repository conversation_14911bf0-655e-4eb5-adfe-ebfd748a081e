import React, { useEffect, useState } from 'react';
import { Leaf } from 'lucide-react';

interface SplashScreenProps {
  onComplete: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onComplete }) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onComplete,0); // Allow fade out animation to complete
    }, 1200); // Show for 0.5 seconds

    return () => clearTimeout(timer);
  }, [onComplete]);

  if (!isVisible) {
    return (
      <div className="fixed inset-0 bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center z-[9999] transition-opacity duration-300 opacity-0 pointer-events-none">
        {/* Content hidden during fade out */}
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center z-[9999] transition-opacity duration-300">
      {/* Animated Background Particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${2 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center">
        {/* Logo Container with Pulse Animation */}
        <div className="mb-6 relative">
          {/* Outer Glow Ring */}
          <div className="absolute inset-0 w-24 h-24 mx-auto bg-white/20 rounded-full animate-ping"></div>
          
          {/* Middle Ring */}
          <div className="absolute inset-2 w-20 h-20 mx-auto bg-white/30 rounded-full animate-pulse"></div>
          
          {/* Logo Background */}
          <div className="relative w-24 h-24 mx-auto bg-white rounded-2xl flex items-center justify-center shadow-2xl transform animate-bounce">
            <Leaf className="w-12 h-12 text-primary-500 animate-pulse" />
          </div>
        </div>

        {/* Brand Text with Staggered Animation */}
        <div className="space-y-2">
          <h1 className="text-4xl md:text-5xl font-black text-white">
            <span className="inline-block animate-slideInLeft" style={{ animationDelay: '0.1s' }}>
              Food
            </span>
            <span className="inline-block animate-slideInRight" style={{ animationDelay: '0.2s' }}>
              Hub
            </span>
          </h1>
          
          <p className="text-xl md:text-2xl font-semibold text-white/90 animate-fadeInUp" style={{ animationDelay: '0.3s' }}>
            Trading
          </p>
          
          {/* Subtitle */}
          <p className="text-sm text-white/70 animate-fadeInUp" style={{ animationDelay: '0.4s' }}>
            Farm to Table Technology
          </p>
        </div>

        {/* Loading Indicator */}
        <div className="mt-8 flex justify-center">
          <div className="w-16 h-1 bg-white/30 rounded-full overflow-hidden">
            <div className="w-full h-full bg-white rounded-full animate-slideProgress"></div>
          </div>
        </div>
      </div>


    </div>
  );
};

export default SplashScreen;
