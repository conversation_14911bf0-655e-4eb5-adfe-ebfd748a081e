import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import SplashScreen from './SplashScreen';

interface SplashWrapperProps {
  children: React.ReactNode;
}

const SplashWrapper: React.FC<SplashWrapperProps> = ({ children }) => {
  const [showSplash, setShowSplash] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const location = useLocation();

  useEffect(() => {
    // Show splash on initial load
    if (isInitialLoad) {
      setShowSplash(true);
      setIsInitialLoad(false);
      return;
    }

    // Show splash on route change (but not on initial load)
    setShowSplash(true);
  }, [location.pathname, isInitialLoad]);

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  return (
    <>
      {showSplash && <SplashScreen onComplete={handleSplashComplete} />}
      <div className={showSplash ? 'opacity-0 pointer-events-none' : 'opacity-100 transition-opacity duration-300'}>
        {children}
      </div>
    </>
  );
};

export default SplashWrapper;
