import { useState, useEffect } from 'react';

const BottomBlurOverlay = () => {
  const [showBottomBlur, setShowBottomBlur] = useState(true);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const documentHeight = document.documentElement.scrollHeight;
      const windowHeight = window.innerHeight;
      const scrollPercentage = currentScrollY / (documentHeight - windowHeight);

      // Show bottom blur when not near the bottom (hide when 90% scrolled)
      setShowBottomBlur(scrollPercentage < 0.9);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div
      className={`fixed bottom-0 left-0 right-0 h-40 pointer-events-none z-40 transition-opacity duration-500 ${
        showBottomBlur ? 'opacity-100' : 'opacity-0'
      }`}
      style={{
        background: 'linear-gradient(to top, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 15%, rgba(255, 255, 255, 0.6) 35%, rgba(255, 255, 255, 0.35) 55%, rgba(255, 255, 255, 0.15) 75%, rgba(255, 255, 255, 0.05) 90%, transparent 100%)',
        backdropFilter: 'blur(12px) saturate(180%)',
        WebkitBackdropFilter: 'blur(12px) saturate(180%)',
        maskImage: 'linear-gradient(to top, black 0%, black 60%, rgba(0,0,0,0.8) 75%, rgba(0,0,0,0.4) 85%, rgba(0,0,0,0.1) 95%, transparent 100%)',
        WebkitMaskImage: 'linear-gradient(to top, black 0%, black 60%, rgba(0,0,0,0.8) 75%, rgba(0,0,0,0.4) 85%, rgba(0,0,0,0.1) 95%, transparent 100%)'
      }}
    />
  );
};

export default BottomBlurOverlay;
