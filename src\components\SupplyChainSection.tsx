import React from 'react';
import { Sprout, Truck, Store, Users, ArrowRight } from 'lucide-react';

const SupplyChainSection = () => {

  const supplyChainSteps = [
    {
      id: 1,
      title: "Farmers",
      subtitle: "Fresh Harvest",
      description: "Direct sourcing from verified farmers ensuring quality and fair pricing",
      icon: <Sprout className="w-8 h-8" />,
      color: "from-green-500 to-emerald-600",
      bgColor: "bg-green-50",
      position: "top-left"
    },
    {
      id: 2,
      title: "Processing",
      subtitle: "Quality Control",
      description: "Advanced sorting, packaging, and quality assurance processes",
      icon: <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
        <div className="w-4 h-4 bg-white rounded-sm"></div>
      </div>,
      color: "from-blue-500 to-indigo-600",
      bgColor: "bg-blue-50",
      position: "top-right"
    },
    {
      id: 3,
      title: "Distribution",
      subtitle: "Smart Logistics",
      description: "Efficient distribution network with real-time tracking and optimization",
      icon: <Truck className="w-8 h-8" />,
      color: "from-orange-500 to-red-600",
      bgColor: "bg-orange-50",
      position: "bottom-left"
    },
    {
      id: 4,
      title: "Partners",
      subtitle: "Retail Network",
      description: "Seamless delivery to retail partners and end consumers",
      icon: <Store className="w-8 h-8" />,
      color: "from-purple-500 to-pink-600",
      bgColor: "bg-purple-50",
      position: "bottom-right"
    }
  ];



  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 to-primary-50/30 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-32 h-32 border-2 border-primary-300 rounded-full"></div>
        <div className="absolute bottom-20 right-20 w-24 h-24 border-2 border-secondary-300 rounded-full"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 border-2 border-accent-300 rounded-full"></div>
      </div>

      <div className="container mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-semibold mb-4">
            <Truck className="w-4 h-4" />
            Supply Chain Excellence
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-black text-gray-900 leading-none mb-6">
            
            <br />
            <span className="bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text text-transparent">
              your table
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience our revolutionary supply chain that connects farmers directly to consumers through 
            technology-driven logistics and partner networks.
          </p>
        </div>

        {/* Main Visual Section */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          
          {/* Video Section */}
          <div className="relative">
            <div className="relative bg-gradient-to-br from-primary-400 to-secondary-500 rounded-3xl p-8 overflow-hidden">
              {/* Video Container */}
              <div className="relative bg-black/10 backdrop-blur-sm rounded-2xl overflow-hidden aspect-video">
                <video
                  className="w-full h-full object-cover"
                  autoPlay
                  muted
                  loop
                  playsInline
                >
                  <source src="/foodhub-gif.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
              

              {/* Floating Stats */}
              <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-xl p-3 shadow-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600">24/7</div>
                  <div className="text-xs text-gray-600">Operations</div>
                </div>
              </div>

              <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-xl p-3 shadow-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-secondary-600">100%</div>
                  <div className="text-xs text-gray-600">Fresh Delivery</div>
                </div>
              </div>
            </div>
          </div>

          {/* Supply Chain Steps */}
          <div className="space-y-6">
            {supplyChainSteps.map((step) => (
              <div
                key={step.id}
                className={`${step.bgColor} rounded-2xl p-6 hover:shadow-lg transition-all duration-300 group cursor-pointer`}
              >
                <div className="flex items-start gap-4">
                  <div className={`w-16 h-16 bg-gradient-to-br ${step.color} rounded-2xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300`}>
                    {step.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-xl font-bold text-gray-800">{step.title}</h3>
                      <span className="text-sm font-semibold text-gray-500">#{step.id}</span>
                    </div>
                    <p className="text-primary-600 font-semibold mb-2">{step.subtitle}</p>
                    <p className="text-gray-600 leading-relaxed">{step.description}</p>
                  </div>
                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-primary-500 group-hover:translate-x-1 transition-all duration-300" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Partner Network Visualization */}
        <div className="bg-white rounded-3xl p-8 md:p-12 shadow-xl">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Our Distribution Network</h3>
            <p className="text-gray-600 text-lg">Connecting farmers to consumers through strategic partnerships</p>
          </div>

          {/* Network Diagram - Redesigned */}
          <div className="relative max-w-5xl mx-auto">
            {/* Grid Layout for Better Positioning */}
            <div className="grid grid-cols-3 gap-8 md:gap-16 items-center min-h-[400px]">

              {/* Top Row */}
              <div className="flex flex-col items-center">
                <div className="w-20 h-20 bg-orange-100 rounded-2xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 group">
                  <Truck className="w-10 h-10 text-orange-600 group-hover:scale-110 transition-transform" />
                </div>
                <p className="text-center mt-3 font-semibold text-gray-800">Logistics</p>
                <p className="text-center text-sm text-gray-500">Smart Distribution</p>
              </div>

              <div className="flex flex-col items-center">
                {/* Central Hub - FoodHub */}
                <div className="relative">
                  <div className="w-32 h-32 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-3xl flex items-center justify-center shadow-2xl">
                    <div className="w-20 h-20 bg-white rounded-2xl flex items-center justify-center">
                      <div className="text-center">
                        <Truck className="w-8 h-8 text-primary-600 mx-auto mb-1" />
                        <div className="text-xs font-bold text-primary-600">Hub</div>
                      </div>
                    </div>
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full animate-pulse"></div>
                </div>
                <p className="text-center mt-4 font-bold text-gray-800 text-lg">FoodHub</p>
                <p className="text-center text-sm text-gray-500">Central Distribution</p>
              </div>

              <div className="flex flex-col items-center">
                <div className="w-20 h-20 bg-purple-100 rounded-2xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 group">
                  <Users className="w-10 h-10 text-purple-600 group-hover:scale-110 transition-transform" />
                </div>
                <p className="text-center mt-3 font-semibold text-gray-800">Consumers</p>
                <p className="text-center text-sm text-gray-500">End Customers</p>
              </div>

              {/* Bottom Row */}
              <div className="flex flex-col items-center">
                <div className="w-20 h-20 bg-green-100 rounded-2xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 group">
                  <Sprout className="w-10 h-10 text-green-600 group-hover:scale-110 transition-transform" />
                </div>
                <p className="text-center mt-3 font-semibold text-gray-800">Farmers</p>
                <p className="text-center text-sm text-gray-500">Fresh Produce</p>
              </div>

              <div className="flex flex-col items-center">
                {/* Connection Indicator */}
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                  <div className="w-8 h-8 border-2 border-dashed border-gray-400 rounded-full animate-spin"></div>
                </div>
                <p className="text-center mt-3 text-sm text-gray-500">Real-time Sync</p>
              </div>

              <div className="flex flex-col items-center">
                <div className="w-20 h-20 bg-blue-100 rounded-2xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 group">
                  <Store className="w-10 h-10 text-blue-600 group-hover:scale-110 transition-transform" />
                </div>
                <p className="text-center mt-3 font-semibold text-gray-800">Retailers</p>
                <p className="text-center text-sm text-gray-500">Partner Stores</p>
              </div>
            </div>

            {/* Connection Lines - SVG Overlay */}
            <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1 }}>
              <defs>
                <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#10B981" stopOpacity="0.4" />
                  <stop offset="50%" stopColor="#3B82F6" stopOpacity="0.6" />
                  <stop offset="100%" stopColor="#8B5CF6" stopOpacity="0.4" />
                </linearGradient>
              </defs>

              {/* Connecting lines from center to all nodes */}
              <line x1="50%" y1="35%" x2="16.67%" y2="15%" stroke="url(#connectionGradient)" strokeWidth="3" strokeDasharray="8,4" className="animate-pulse" />
              <line x1="50%" y1="35%" x2="83.33%" y2="15%" stroke="url(#connectionGradient)" strokeWidth="3" strokeDasharray="8,4" className="animate-pulse" />
              <line x1="50%" y1="65%" x2="16.67%" y2="85%" stroke="url(#connectionGradient)" strokeWidth="3" strokeDasharray="8,4" className="animate-pulse" />
              <line x1="50%" y1="65%" x2="83.33%" y2="85%" stroke="url(#connectionGradient)" strokeWidth="3" strokeDasharray="8,4" className="animate-pulse" />
            </svg>
          </div>

          {/* Network Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 pt-8 border-t border-gray-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary-600">500+</div>
              <div className="text-sm text-gray-600">Active Farmers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-secondary-600">50+</div>
              <div className="text-sm text-gray-600">Partner Stores</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">24/7</div>
              <div className="text-sm text-gray-600">Operations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">10k+</div>
              <div className="text-sm text-gray-600">Happy Customers</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SupplyChainSection;
