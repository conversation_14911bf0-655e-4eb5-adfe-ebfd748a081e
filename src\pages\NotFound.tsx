import { useLocation, Link } from "react-router-dom";
import { useEffect } from "react";
import { Leaf, Home, ArrowRight, Sprout, Truck, Users } from "lucide-react";
import { Button } from "@/components/ui/button";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Floating Vegetables */}
        <div className="absolute top-20 left-10 w-16 h-16 bg-green-100 rounded-full flex items-center justify-center animate-float opacity-60">
          <Sprout className="w-8 h-8 text-green-500" />
        </div>
        <div className="absolute top-40 right-20 w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center animate-float opacity-60" style={{ animationDelay: '1s' }}>
          <div className="w-6 h-6 bg-orange-400 rounded-full"></div>
        </div>
        <div className="absolute bottom-40 left-20 w-14 h-14 bg-red-100 rounded-full flex items-center justify-center animate-float opacity-60" style={{ animationDelay: '2s' }}>
          <div className="w-7 h-7 bg-red-400 rounded-full"></div>
        </div>
        <div className="absolute bottom-20 right-10 w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center animate-float opacity-60" style={{ animationDelay: '0.5s' }}>
          <div className="w-5 h-5 bg-yellow-400 rounded-full"></div>
        </div>

        {/* Delivery Truck */}
        <div className="absolute top-1/2 right-32 transform -translate-y-1/2 opacity-30">
          <Truck className="w-20 h-20 text-primary-300 animate-pulse" />
        </div>

        {/* Farmers */}
        <div className="absolute bottom-32 left-32 opacity-30">
          <Users className="w-16 h-16 text-secondary-300 animate-pulse" style={{ animationDelay: '1.5s' }} />
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4">
        <div className="text-center max-w-2xl mx-auto">
          {/* Brand Logo */}
          <div className="mb-8">
            <div className="w-24 h-24 mx-auto bg-gradient-to-br from-primary-500 to-secondary-500 rounded-3xl flex items-center justify-center shadow-2xl mb-4 animate-bounce">
              <Leaf className="w-12 h-12 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800">FoodHub Trading</h2>
            <p className="text-sm text-gray-600">Farm to Table Technology</p>
          </div>

          {/* 404 Error */}
          <div className="mb-8">
            <h1 className="text-8xl md:text-9xl font-black text-transparent bg-gradient-to-r from-primary-500 to-secondary-500 bg-clip-text mb-4 animate-pulse">
              404
            </h1>
            <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Oops! This field is empty
            </h3>
            <p className="text-lg text-gray-600 mb-2">
              Looks like this page got lost in our supply chain!
            </p>
            <p className="text-base text-gray-500">
              Don't worry, our delivery network will help you find your way back to fresh content.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
            <Link to="/">
              <Button className="bg-primary-500 hover:bg-primary-600 text-white rounded-full px-8 py-4 text-lg font-semibold flex items-center gap-2">
                <Home className="w-5 h-5" />
                Back to Farm
                <ArrowRight className="w-5 h-5" />
              </Button>
            </Link>

            <Link to="/contact">
              <Button variant="outline" className="border-primary-500 text-primary-600 hover:bg-primary-50 rounded-full px-8 py-4 text-lg font-semibold">
                Contact Support
              </Button>
            </Link>
          </div>

          {/* Quick Links */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-md mx-auto">
            <Link to="/about" className="group">
              <div className="bg-white rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Leaf className="w-4 h-4 text-primary-600" />
                </div>
                <p className="text-sm font-semibold text-gray-700">About</p>
              </div>
            </Link>

            <Link to="/career" className="group">
              <div className="bg-white rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Users className="w-4 h-4 text-secondary-600" />
                </div>
                <p className="text-sm font-semibold text-gray-700">Career</p>
              </div>
            </Link>

            <Link to="/partner" className="group">
              <div className="bg-white rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Truck className="w-4 h-4 text-accent-600" />
                </div>
                <p className="text-sm font-semibold text-gray-700">Partner</p>
              </div>
            </Link>

            <Link to="/contact" className="group">
              <div className="bg-white rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                  <Sprout className="w-4 h-4 text-primary-600" />
                </div>
                <p className="text-sm font-semibold text-gray-700">Contact</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
